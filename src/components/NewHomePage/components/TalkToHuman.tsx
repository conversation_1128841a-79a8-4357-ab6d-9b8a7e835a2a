import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import SectionContainerXSmall from "@/components/globals/SectionContainerXSmall";
import { BodyMedium, Heading2XLarge } from "@/components/UI/Typography";
import React from "react";
import { Button } from "@/components/UI/Button";
import Image from "next/image";

const TalkToHuman = () => {
  return (
    <SectionContainerMedium className="!pr-4">
      <div className="bg-primary-100 mt-12 md:mt-20 border border-primary-300 rounded-xl relative ">
        <SectionContainerXSmall className="flex flex-col relative z-10">
            <Heading2XLarge className="text-primary-800 mt-12">
              Still deciding? Talk to a human.
            </Heading2XLarge>
            <BodyMedium className="mt-6 text-neutral-800 max-w-[650px]">
            Behind every policy, there's a bunch of humans who care about getting it right for you. Drop us a message - we'll be here.
            </BodyMedium>
          <div className="flex justify-center md:justify-start ">
            <Button className="rounded-xl py-4  border-none w-[170px] mt-8 z-10 relative">
              Talk To A Human
            </Button>
          </div>
        </SectionContainerXSmall>
        <div className="absolute bottom-0 right-0 hidden md:block translate-x-4 md:translate-x-8 lg:translate-x-12 lg:translate-y-5">
          <Image
            width={280}
            height={280}
            src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5a9d-8534-7170-8565-754bf62895ce/boy.svg"
            alt="Boy illustration"
            className="w-[220px] md:w-[300px] lg:w-[340px] xl:w-[390px] h-auto object-contain"
          />
        </div>
      </div>
    </SectionContainerMedium>
  );
};

export default TalkToHuman;