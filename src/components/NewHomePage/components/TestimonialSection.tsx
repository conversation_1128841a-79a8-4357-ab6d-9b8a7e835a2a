"use client";

import Pill from "@/components/globals/DSComponentsV0/Pill";
import SectionContainer from "@/components/globals/SectionContainer";
import {
  BodyLarge,
  BodyMedium,
  HeadingLarge,
  HeadingXLarge,
} from "@/components/UI/Typography";
import { useEffect, useState } from "react";
import TestimonialCard from "./TestimonialCard";

const TestimonialSection = () => {
  const storyPoints = [
    "Helped 43,000+ families feel protected, not pressured.",
    "Guided first-time buyers through their very first claim — calmly.",
    "Supported parents getting their first health cover for kids.",
    "Sorted 15,000+ claims that would've otherwise been stressful.",
    "Answered real questions at 11 PM — because emergencies don't wait.",
    "Saved users ₹2.3 Cr worth of unnecessary add-ons last year.",
  ];

  const testimonials = [
    {
      name: "<PERSON>",
      video_url: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5b56-eb50-75dc-ab1a-28a979a12fa7/5752729-uhd_3840_2160_30fps.mp4",
    },
    {
      name: "<PERSON>",
      statement: "I recently received invaluable assistance from Chethana and Abhishek at OneAssure during my claim settlement process with ICICI Lombard Mediclaim. Their consistent communication and proactive approach ensured that all issues were addressed promptly at each stage, leading to a timely settlement of my claim. Both Chethana and Abhishek exhibited professionalism and thoroughness in their interactions, keeping me informed of the progress regularly. I am sincerely grateful to them for their exceptional support."
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % storyPoints.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [storyPoints.length]);

  // Function to get the style for each position
  const getPositionStyle = (position: number) => {
    switch (position) {
      case 0: // 1st position - light (most transparent)
        return "opacity-30 text-primary-600 bg-transparent scale-95";
      case 1: // 2nd position - darker
        return "opacity-70 text-primary-700 bg-transparent scale-98";
      case 2: // 3rd position (middle) - green background with white text
        return "opacity-100 text-white bg-secondary-400 scale-100 shadow-md";
      case 3: // 4th position - darker (same as 2nd)
        return "opacity-70 text-primary-700 bg-transparent scale-98";
      case 4: // 5th position - light (same as 1st)
        return "opacity-30 text-primary-600 bg-transparent scale-95";
      default:
        return "opacity-0 scale-90";
    }
  };

  return (
    <div className="bg-primary-100 pt-16 pb-28 overflow-hidden">
      <SectionContainer>
        <Pill
          pill="Success Stories"
          border={false}
          textColor="text-secondary-400"
          bgColor="bg-white"
          className="mb-3"
        />
        <HeadingXLarge className="text-primary-800 text-center font-medium">
          Hear From People Like You
        </HeadingXLarge>
      </SectionContainer>

      <div className="flex">
        <div className="flex flex-col gap-4 flex-1 pl-28 pr-8">
          <HeadingLarge className="text-primary-800 font-semibold ml-10">
            Every Policy Has a Story
          </HeadingLarge>

          <div className="relative h-[400px] flex items-center overflow-hidden">
            <div className="w-full relative" style={{ height: '350px' }}>
              {storyPoints.map((point, index) => {
                const relativePosition = (index - currentIndex + storyPoints.length) % storyPoints.length;
                const position = relativePosition < 5 ? relativePosition : -1;
                const isVisible = position >= 0;

                const topPosition = position * 70;

                return (
                  <div
                    key={index}
                    className={`absolute left-0 right-0 transition-all duration-700 ease-in-out ${
                      isVisible ? "block" : "hidden"
                    }`}
                    style={{
                      top: `${topPosition}px`,
                    }}
                  >
                    <div
                      className={`px-6 py-5 rounded-full transition-all duration-700 ease-in-out ${getPositionStyle(
                        position
                      )}`}
                    >
                      {position === 2 ? (
                        <BodyLarge className="font-medium">
                          {point}
                        </BodyLarge>
                      ) : (
                        <BodyMedium>{point}</BodyMedium>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        <div className="flex-1 flex overflow-hidden p-6 gap-8 bg-white rounded-l-xl">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              name={testimonial.name}
              statement={testimonial.statement}
              video_url={testimonial.video_url}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default TestimonialSection;