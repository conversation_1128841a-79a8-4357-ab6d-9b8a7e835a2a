import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import SectionHeader from "@/components/globals/SectionHeader";
import { Button } from "@/components/UI/Button";
import { HeadingLarge } from "@/components/UI/Typography";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import Image from "next/image";
import React from "react";

type AskAnythingSectionProps = {
  pill: string;
  heading: string;
  subheading: string;
};

const AskAnythingSection = ({
  pill,
  heading,
  subheading,
}: AskAnythingSectionProps) => {
  return (
    <SectionContainerLarge className="mt-12 md:mt-20 !p-0 ">
      <SectionHeader
        pill={pill}
        heading={heading}
        subheading={subheading}
        component="h2"
        className="px-6 "
      />
      <div className="bg-primary-200 overflow-hidden hidden md:flex flex-col lg:flex-row rounded-4xl md:rounded-5xl  mx-6 gap-8 px-5 md:px-0">
        <div className="flex flex-col items-center text-center  lg:flex-1">
          <HeadingLarge className=" mt-20 text-primary-800 font-semibold mb-3">
            Confused About Claims,
          </HeadingLarge>
          <HeadingLarge className="font-semibold text-primary-800 mb-6">
            We're Here to Help
          </HeadingLarge>
          <Button className="rounded-xl border-none px-8 py-4 text-lg">
            Talk To A Human
          </Button>
          <div className="overflow-hidden ">
            <Image
              src={
                "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5995-a5d8-799a-823b-4a6b534dfc65/girl.png"
              }
              alt="Customer service representative"
              width={435}
              height={500}
              className="hidden lg:block rounded-lg"
            />
          </div>
        </div>
        <div className="relative overflow-hidden lg:flex-1 flex justify-end">
          <div className="relative w-full flex justify-center items-center">
            {/* Phone Frame */}
            <div className="absolute top-16 right-13">
              <Image
                src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5998-a113-7942-944b-e66f65b6b16f/mobile.svg"
                alt="Mobile app interface"
                className="rounded-lg"
                height={500}
                width={435}
              />

              {/* Chat Image inside Phone */}
              <div className="absolute -top-12 left-4 right-4 h-[73%] overflow-y-scroll no-scrollbar rounded-lg">
                <Image
                  src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5a45-06a3-7e94-a172-c4ddea7e5ad1/chats.svg"
                  alt="Chat interface"
                  width={435}
                  height={500}
                  className="w-full h-full rounded-lg pointer-events-none select-none"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <MobileCarousel totalSlides={1} className="md:hidden">
        <MobileCarouselItem>
          <div className="bg-secondary-300 flex flex-col max-h-[500px] rounded-4xl overflow-hidden gap-8 px-5 pt-8">
            {/* Text and Button Section */}
            <div className="flex flex-col items-center text-center">
              <HeadingLarge className="text-primary-800 font-normal mb-3">
                Confused About Claims,
              </HeadingLarge>
              <HeadingLarge className="font-bold text-primary-800 mb-6">
                We're Here to Help
              </HeadingLarge>
              <Button className="rounded-xl border-none px-8 py-4 text-lg mb-6">
                Talk To A Human
              </Button>
              <div className="relative ">
                {/* Phone Frame */}
                <Image
                  src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5998-a113-7942-944b-e66f65b6b16f/mobile.svg"
                  alt="Mobile app interface"
                  className="rounded-lg"
                  height={500}
                  width={435}
                />
                <div className="absolute -top-12 left-4 right-4 h-[73%] overflow-y-scroll no-scrollbar rounded-lg">
                  <Image
                    src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5a45-06a3-7e94-a172-c4ddea7e5ad1/chats.svg"
                    alt="Chat interface"
                    width={435}
                    height={500}
                    className="w-full h-full rounded-lg pointer-events-none select-none"
                  />
                </div>
              </div>
            </div>
          </div>
        </MobileCarouselItem>
      </MobileCarousel>
    </SectionContainerLarge>
  );
};

export default AskAnythingSection;
