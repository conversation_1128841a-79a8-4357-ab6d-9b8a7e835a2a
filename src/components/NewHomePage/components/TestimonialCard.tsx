import { BodyLarge, HeadingSmall } from "@/components/UI/Typography";

export type TestimonialCardProps = {
  name: string;
  statement?: string;
  video_url?: string;
};
// at least one of video or statement is required

const TestimonialCard = ({
  name,
  statement,
  video_url,
}: TestimonialCardProps) => {
  return (
    <div className="bg-primary-800 flex flex-col justify-center gap-4 p-6 rounded-xl">
      <div>
        {statement && <BodyLarge className="text-white font-regular min-w-[300px]">{statement}</BodyLarge>}
        {video_url && (
          <div className="p-2 bg-white rounded-xl">
            <video src={video_url} controls={true} className="w-full h-auto min-w-[300px] max-h-[420px] max-w-[420px] rounded-xl" />
          </div>
        )}
      </div>
      <div>
        <HeadingSmall className="text-white">{name}</HeadingSmall>
      </div>
    </div>
  );
};

export default TestimonialCard;
