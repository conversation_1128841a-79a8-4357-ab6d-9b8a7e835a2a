import SectionHeader from "@/components/globals/DSComponentsV0/SectionHeaderWithParse";
import PillBadge from "@/components/globals/PillBadge";
import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import Image from "next/image";
import {
  BodyLarge,
  BodyMedium,
  BodySmall,
  HeadingXLarge,
} from "@/components/UI/Typography";

type StepItem = {
  icon: string;
  title: string;
  description: string;
};

type HowOneAssureWork = {
  pill: string;
  heading: string;
  steps: StepItem[];
};

const HowOneAssureWork = ({
  pill,
  heading,
  steps,
}: {
  pill: string;
  heading: string;
  steps: StepItem[];
}) => {
  return (
    <SectionContainerSmall className="!px-0 flex flex-col md:flex-row items-center md:mt-20 gap-6 md:gap-28">
      {/* <SectionHeader pill={pill} heading={heading} component="h2" /> */}
      <div className="flex flex-col gap-4">
        <PillBadge pill={pill} as="p"/>
        <HeadingXLarge className="text-center text-neutral-1100 font-semibold">
          {heading}
        </HeadingXLarge>
      </div>
      <div className="relative flex flex-col gap-4 ">
        {steps.map((step, index) => (
          <div key={index} className="flex gap-1 md:gap-6">
            <Image src={step.icon} alt={step.title} width={20} height={20} />
            <div className="border border-primary-200 bg-white shadow-sm rounded-lg p-4 w-[300px] md:w-[370px] flex flex-col gap-2">
              <BodyLarge className="font-bold text-gray-900">
                {step.title}
              </BodyLarge>
              <BodySmall className="text-gray-600">
                {step.description}
              </BodySmall>
            </div>
          </div>
        ))}
      </div>
    </SectionContainerSmall>
  );
};

export default HowOneAssureWork;
