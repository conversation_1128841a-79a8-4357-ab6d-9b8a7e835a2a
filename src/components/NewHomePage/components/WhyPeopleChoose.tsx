import Image from "next/image";
import PillBadge from "@/components/globals/PillBadge";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { BodyMedium, HeadingLarge, HeadingMedium, HeadingSmall, HeadingXLarge } from "@/components/UI/Typography";
import React from "react";

type whyPeopleChooseCard = {
  icon: string;
  title: string;
  description: string;
};

type WhyPeopleChooseProps = {
  pill_Content: string;
  title: string;
  cards: whyPeopleChooseCard[];
};

const WhyPeopleChoose = ({
  pill_Content,
  title,
  cards,
}: WhyPeopleChooseProps) => {
  return (
    <SectionContainerLarge className="pt-12 flex flex-col gap-8">
      <div className="flex flex-col items-center justify-center gap-4 mb-4 md:mb-8">
        <PillBadge pill={pill_Content} />
        <HeadingXLarge className="font-semibold text-primary-800 text-center">{title}</HeadingXLarge>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 ">
        {cards.map((card, index) => (
          <div
            key={index}
            className="flex flex-col  gap-4 items-center"
          >
            <div className="relative w-16 h-16 md:w-20 md:h-20 flex items-center ">
              <Image
                src={card.icon}
                alt={card.title}
                fill
                style={{ objectFit: 'contain' }}
              />
            </div>
            
            <HeadingSmall className="font-bold text-neutral-900 text-center">
              {card.title}
            </HeadingSmall>
            
            <BodyMedium className=" font-normal text-primary-800 text-center">
              {card.description}
            </BodyMedium>
          </div>
        ))}
      </div>
    </SectionContainerLarge>
  );
};

export default WhyPeopleChoose;
