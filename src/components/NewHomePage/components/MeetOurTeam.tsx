import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { BodyMedium, DisplayMedium } from "@/components/UI/Typography";
import React from "react";
import Image from "next/image";

const MeetOurTeam = () => {
  return (
    <SectionContainerLarge className="mt-12 md:mt-20 flex flex-col gap-8">
      <DisplayMedium className="text-primary-800 text-center font-semibold">
        Meet The Team
      </DisplayMedium>
      <BodyMedium className="text-center text-primary-800">
      Meet the people dedicated to finding you the right plan, not just selling you one.
      </BodyMedium>
      <div className="flex-1">
        <Image
          src={
            "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5ab3-cbe7-7913-9911-e8bd0b828a7a/PHOTO-2025-05-07-13-14-59 1.svg"
          }
          alt="our-team-1"
          width={1000}
          height={1000}
          className="w-full h-full object-contain"
        />
      </div>
    </SectionContainerLarge>
  );
};

export default MeetOurTeam;
